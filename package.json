{"name": "com.chargoon.cloud.svc.print", "version": "0.0.43", "description": "", "author": "<EMAIL>", "private": true, "license": "UNLICENSED", "scripts": {"start": "node ./src", "start:dev": "nodemon -r dotenv/config ./src", "start:local": "nodemon -L ./src/index.js", "test:e2e": "mocha -r dotenv/config --timeout 10000 --exit", "lint": "eslint \"{src,apps,libs,test}/**/*.{js, jsx}\" --fix"}, "dependencies": {"@babel/core": "7.26.10", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-object-rest-spread": "7.20.7", "@babel/plugin-transform-arrow-functions": "7.25.9", "@babel/plugin-transform-async-to-generator": "7.25.9", "@babel/plugin-transform-runtime": "7.26.10", "@babel/preset-env": "7.26.9", "@babel/preset-react": "7.26.3", "@babel/register": "7.25.9", "@pdf-lib/fontkit": "1.1.1", "awat-fluent-logger": "0.0.1", "com-chargoon-cloud-front-print": "1.0.204", "com.chargoon.cloud.svc.common": "0.0.284", "cors": "2.8.5", "decorate-component-with-props": "1.2.1", "express": "5.1.0", "ignore-styles": "5.0.1", "jalaali-js": "1.2.8", "jsdom": "26.1.0", "pdf-lib": "1.17.1", "puppeteer": "24.7.2", "react": "18.3.1", "react-dom": "18.3.1", "react-jss": "10.10.0", "winston": "3.17.0"}, "devDependencies": {"dotenv": "16.5.0", "eslint": "9.25.1", "eslint-plugin-react": "7.37.5", "mocha": "11.1.0", "nodemon": "3.1.10", "supertest": "7.1.0"}, "nodemonConfig": {"ext": "js,jsx"}}