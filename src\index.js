const PKG = require('../package.json');
const tracer = require('com.chargoon.cloud.svc.common/dist/tracing/tracer');
tracer.default(PKG, null, { attributeCountLimit: 5 });

const express = require('express');
const path = require('path');
require('ignore-styles');
require('@babel/register')({
  configFile: './babel.config.js',
  extensions: ['.jsx', '.js'],
  only: [
    'node_modules/com-chargoon-cloud-front-print',
    'node_modules/com-chargoon-cloud-front-editor',
    'node_modules/com-chargoon-cloud-front-editor2',
    'src',
  ]
});
const exportLetterAsPdf = require('./routes/render/exportLetterAsPdf');
const logger = require('./utils/logger');

const app = express();
app.use(require('cors')());
app.use(express.json({ limit: '50mb' })); // unlimited purpose...

// Serve static font files
app.use('/fonts', express.static(path.join(__dirname, 'letter/fonts')));
app.post('/render/image', exportLetterAsPdf);
app.get('/ready', (req, res) => {
  res.status(200).send({
    statusCode: 200,
    message: 'READY',
  });
});
app.post('/log', (req, res) => {
  logger.info(req?.body?.text);
  res.status(201).send(null);
});
app.use((err, req, res, next) => {
  logger.error(err.message);
  res.status(500).send(err?.message || 'system error');
});
const server = app.listen(process.env.PORT || 3000, '0.0.0.0', () => {
  logger.info(
    `listening at http://${server.address().address}:${server.address().port}`,
  );
});

module.exports = app;
