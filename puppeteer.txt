PUPPETEER_SKIP_CHROMIUM_DOWNLOAD="true"
docker build -t com.chargoon.cloud.svc.print:0.0.1 .
docker run -it --name print -p 3000:3000 com.chargoon.cloud.svc.print:0.0.1

// For <PERSON>'s PC
$env:PUPPETEER_EXECUTABLE_PATH = "C:\Users\<USER>\AppData\Local\Chromium\Application\chrome.exe"


$env:PUPPETEER_EXECUTABLE_PATH="C:\Program Files\Google\Chrome\Application\chrome.exe"
$env:PUPPETEER_SKIP_DOWNLOAD="true"
$env:CONNECT_FLUENT_TRANSPORT="false"
