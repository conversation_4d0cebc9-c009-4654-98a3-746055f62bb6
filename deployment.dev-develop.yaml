apiVersion: v1
kind: Namespace
metadata:
  name: develop
---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: com-chargoon-cloud-svc-print
  namespace: develop
  labels:
    app: com-chargoon-cloud-svc-print
spec:
  replicas: 1
  selector:
    matchLabels:
      app: com-chargoon-cloud-svc-print
  template:
    metadata:
      labels:
        app: com-chargoon-cloud-svc-print
    spec:
      imagePullSecrets:
        - name: dockerregistry
      containers:
        - name: com-chargoon-cloud-svc-print
          image: localhost:32001/com.chargoon.cloud.svc.print:0.0.43
          imagePullPolicy: Always
          env:
            - name: FLUENTD_HOSTNAME
              value: "fluentd.develop.svc.cluster.local"
            - name: FLUENTD_PORT
              value: "24224"
            - name: FLUENTD_MESSAGE_LIMIT
              value: "1500"
            - name: FLUENTD_RECONNECT_INTERVAL
              value: "600000"
            - name: CONNECT_FLUENT_TRANSPORT
              value: "true"
            - name: JAEGER_HOST
              value: "jaeger-agent.develop.svc.cluster.local"
            - name: JAEGER_PORT
              value: "6832"
          resources:
            requests:
              memory: "500Mi"
              cpu: "300m"
            limits:
              memory: "600Mi"
              cpu: "350m"
          ports:
            - containerPort: 3000
      restartPolicy: Always
---
kind: Service
apiVersion: v1
metadata:
  name: com-chargoon-cloud-svc-print
  namespace: develop
  labels:
    app: com-chargoon-cloud-svc-print
spec:
  type: NodePort
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
      nodePort: 31821
  selector:
    app: com-chargoon-cloud-svc-print
