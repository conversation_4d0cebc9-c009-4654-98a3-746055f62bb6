@ECHO OFF

@REM ECHO APP
@REM call npm run build

ECHO IMG
call docker build -t com.chargoon.cloud.svc.print:0.0.2 .

ECHO TAG
call docker tag com.chargoon.cloud.svc.print:0.0.2 devbuild-srv.chargoon.net:32000/com.chargoon.cloud.svc.print:0.0.2

ECHO PUSH
call docker push devbuild-srv.chargoon.net:32000/com.chargoon.cloud.svc.print:0.0.2

if "%~1"=="" goto rollout

ECHO DEPLOY
<NAME_EMAIL> mkdir -p /home/<USER>/k8s/com.chargoon.cloud.svc.print
call scp deployment.dev.yaml <EMAIL>:/home/<USER>/k8s/com.chargoon.cloud.svc.print/deployment.dev.yaml
<NAME_EMAIL> kubectl apply -f /home/<USER>/k8s/com.chargoon.cloud.svc.print/deployment.dev.yaml
goto exit

:rollout
ECHO ROLLOUT
<NAME_EMAIL> kubectl rollout restart deployment/com-chargoon-cloud-svc-print

:exit
