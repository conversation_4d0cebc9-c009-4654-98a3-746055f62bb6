'use strict';
module.exports = {
  findTokenInSection(layout, token) {
    for (const l of ['headers', 'footers']) {
      const section = layout?.sections[l];
      for (const sectionKey of Object.keys(section)) {
        if (section[sectionKey]?.children[token]) {
          return {
            [token]: section[sectionKey]?.children[token],
            section: l,
          };
        }
      }
    }
    return null;
  }
}
