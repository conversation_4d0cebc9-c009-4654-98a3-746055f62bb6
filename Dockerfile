FROM localhost:32001/node:20.18.2-alpine3.21
RUN sed -i 's/https/http/' /etc/apk/repositories
RUN apk update \
    && apk add --no-cache \
    chromium \
    nss \
    freetype \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    && rm -rf /var/cache/* \
    && mkdir /var/cache/apk
ARG NODE_ENV=production
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser \
    NODE_ENV=${NODE_ENV}
WORKDIR /usr/src/app
COPY ./package.json .
COPY ./.npmrc .
RUN npm install --omit=dev --ignore-scripts && npm cache clean --force
COPY ./babel.config.js .
COPY ./src ./src
CMD ["node", "./src"]