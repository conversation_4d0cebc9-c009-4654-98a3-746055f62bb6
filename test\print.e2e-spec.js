const dotenv = require('dotenv');
const path = require('path');
const os = require('os')
dotenv.config({ path: path.join(__dirname, '../.env.e2e'), override: true });
process.env.PUPPETEER_EXECUTABLE_PATH = process.env.PUPPETEER_EXECUTABLE_PATH.replace("\*",os.userInfo().username)
const request = require('supertest');
const assert = require('assert');
const fs = require('fs');
const app = require('../src');
const letter = require('./common-letter');

describe('Print test (e2e)', () => {

  it('Should generate pdf from dummy data and compare with related dummy pdf', async () => {
    const response = await request(app)
      .post('/render/image')
      .send(letter)
      .expect('Content-Type', 'application/pdf')
      .expect(200)
      .buffer()
      .parse((res, callback) => {
        res.setEncoding('binary');
        res.data = '';
        res.on('data', (chunk) => {
          res.data += chunk;
        });
        res.on('end', () => {
          callback(null, Buffer.from(res.data, 'binary'));
        });
      });
    const binaryLetter = fs.readFileSync(path.join(__dirname, 'common-letter.pdf'));
    assert.ok(binaryLetter.compare(response.body), 'Binary is not equal');
  });
});
