docker build -t chargoon-develop/com.chargoon.cloud.svc.print:0.0.43 .
docker tag chargoon-develop/com.chargoon.cloud.svc.print:0.0.43 devbuild-srv.chargoon.net:32001/com.chargoon.cloud.svc.print:0.0.43
docker push devbuild-srv.chargoon.net:32001/com.chargoon.cloud.svc.print:0.0.43
cat deployment.dev-develop.yaml | ssh -p 22443 -i ~/devbuild <EMAIL> kubectl apply -f -
ssh -p 22443 -i ~/devbuild <EMAIL> kubectl rollout restart deployment com-chargoon-cloud-svc-print -n develop
