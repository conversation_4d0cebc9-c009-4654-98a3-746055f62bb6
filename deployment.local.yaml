kind: Deployment
apiVersion: apps/v1
metadata:
  name: com-chargoon-cloud-svc-print
spec:
  replicas: 1
  selector:
    matchLabels:
      app: com-chargoon-cloud-svc-print
  template:
    metadata:
      labels:
        app: com-chargoon-cloud-svc-print
    spec:
      containers:
        - name: com-chargoon-cloud-svc-print
          image: localhost:32000/com.chargoon.cloud.svc.print:0.0.1
          imagePullPolicy: Always
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "256Mi"
              cpu: "250m"
          ports:
            - containerPort: 3000
      restartPolicy: Always
---
kind: Service
apiVersion: v1
metadata:
  name: com-chargoon-cloud-svc-print
spec:
  type: NodePort
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
      nodePort: 31820
  selector:
    app: com-chargoon-cloud-svc-print
