const path = require('node:path');

module.exports = (options) => {
  const { letter, layout } = options;

  const printFilePath = `file://${path.resolve(
    __dirname,
    '../../node_modules/com-chargoon-cloud-front-print/dist/print/src/print/index.html',
  )}#${encodeURIComponent(JSON.stringify({ letter, layout }))}`;

  return printFilePath;
  //     <head>
  //       <meta charset="utf-8">
  //       <script src="https://unpkg.com/pagedjs/dist/paged.polyfill.js"></script>
  //       <style>
  //         @page {
  //           size: ${layout.paper.width}cm ${layout.paper.height}cm;
  //           @top-center {
  //             content: element(AH);
  //           }
  //           @bottom-center {
  //             content: element(AF);
  //           }
  //         }
  //       </style>
  //       <style>
  //         html {
  //           -webkit-print-color-adjust: 'exact';
  //           -webkit-text-size-adjust: none;
  //         }
  //         table, figure {
  //           margin: 0;
  //           padding: 0;
  //           border: 0;
  //           text-size-adjust: none;
  //           -webkit-text-size-adjust: none;
  //           font-size: inherit;
  //         }
  //         table, figure {
  //           margin: 0;
  //           padding: 0;
  //           border: 0;
  //         }
  //         .alignment-DraftStyleDefault-alignLeft {
  //           text-align: left;
  //         }
  //         .alignment-DraftStyleDefault-alignCenter {
  //           text-align: center;
  //         }
  //         .alignment-DraftStyleDefault-alignRight {
  //           text-align: right;
  //         }
  //         .alignment-DraftStyleDefault-alignJustify {
  //           text-align: justify;
  //         }
  //         .alignment-DraftStyleDefault-rtl {
  //           direction: rtl;
  //         }
  //         .alignment-DraftStyleDefault-ltr {
  //           direction: ltr;
  //         }
  //       </style>
  //       <style>${sheets.toString()}</style>
  //     </head>
  //     ${body}
  //   </html>
  // `;
};
