const A4 = {
  paper: {
    width: 21,
    height: 29.7,
    background: {
      imageBase64: '',
    },
    fontFamily: 'vazir',
  },
  margins: {
    top: 1.5,
    bottom: 1.5,
    left: 1,
    right: 1,
  },
  sections: {
    content: {
      children: {},
    },
    headers: {
      all: {
        height: 4.7,
        children: {
          date: {
            top: 0.1,
            right: 15,
            label: 'تاریخ',
            fontSize: '14',
            fontWeight: 'bold',
          },
          letterNumber: {
            top: 1.6,
            right: 15,
            label: 'شماره نامه',
            fontSize: '14',
            fontWeight: 'bold',
          },
          attachment: {
            top: 3.1,
            right: 15,
            label: 'پیوست',
            fontSize: '14',
            fontWeight: 'bold',
          },
        },
      },
      first: {
        height: 5,
        children: {
          sender: {
            top: 0.3,
            right: 0.5,
            label: 'فرستنده',
            fontSize: '14',
            isSingleLine: false,
            isTitleFullNameFirst: false,
            isFullNameOnly: false,
            fontWeight: 'bold',
          },
          recipient: {
            top: 1.8,
            right: 0.5,
            label: 'گیرنده',
            fontSize: '14',
            isSingleLine: false,
            isTitleFullNameFirst: false,
            isFullNameOnly: false,
            fontWeight: 'bold',
            lines: [
              {
                format: {
                  dir: 'rtl',
                  align: 'left',
                },
                segments: ['personTitle', 'firstName', 'lastName'],
              },
              { format: {}, segments: ['jobTitle', 'business'] },
              // { format: {}, segments: ['personTitle', 'firstName', 'lastName', 'jobTitle', 'business'] },
            ]
          },
          subject: {
            top: 3.3,
            right: 0.5,
            label: 'موضوع',
            fontSize: '14',
            fontWeight: 'bold',
          },
        },
      },
    },
    footers: {
      all: {
        height: 3,
        children: {},
      },
      last: {
        height: 4,
        children: {
          signature: {
            top: 1,
            right: 7.5,
            label: 'امضاء',
            fontSize: '14',
            isSignerName: true,
            isSingleLine: false,
            isTitleFullNameFirst: false,
            isFullNameOnly: false,
            fontWeight: 'bold',
            align: 'center',
            maxSize: '100',
          },
        },
      },
    },
    reserved: {
      children: {
        logo: {
          top: 2.5,
          right: -6,
          label: 'لوگو',
          maxSize: '100',
        },
        stamp: {
          top: 2.5,
          right: -6,
          label: 'مهر',
          maxSize: '100',
        },
        priority: {
          top: 5,
          right: -6,
          label: 'اولویت',
          fontSize: '14',
          fontWeight: 'bold',
        },
        confidentiality: {
          top: 7.5,
          right: -6,
          label: 'محرمانگی',
          fontSize: '14',
          fontWeight: 'bold',
        },
        pageNumber: {
          top: 10,
          right: -6,
          label: 'شماره صفحه',
          fontSize: '14',
          fontWeight: 'bold',
        },
        cc: {
          top: 12.5,
          right: -6,
          label: 'گیرندگان رونوشت',
          fontSize: '14',
          isSingleLine: false,
          isTitleFullNameFirst: false,
          isFullNameOnly: false,
          fontWeight: 'bold',
        },
      },
    },
  },
};

const A5 = {
  paper: {
    width: 14.8,
    height: 21,
    background: {
      imageBase64: '',
    },
    fontFamily: 'vazir',
  },
  margins: {
    top: 1,
    bottom: 1,
    left: 0.5,
    right: 0.5,
  },
  sections: {
    content: {
      children: {},
    },
    headers: {
      all: {
        height: 2.5,
        children: {
          date: {
            top: 0.25,
            right: 9.5,
            label: 'تاریخ',
            fontSize: '14',
            fontWeight: 'bold',
          },
        },
      },
      first: {
        height: 5,
        children: {
          sender: {
            top: 0.3,
            right: 0.5,
            label: 'فرستنده',
            fontSize: '14',
            isSingleLine: false,
            isTitleFullNameFirst: false,
            isFullNameOnly: false,
            fontWeight: 'bold',
          },
          recipient: {
            top: 1.8,
            right: 0.5,
            label: 'گیرنده',
            fontSize: '14',
            isSingleLine: false,
            isTitleFullNameFirst: false,
            isFullNameOnly: false,
            fontWeight: 'bold',
          },
          subject: {
            top: 3.3,
            right: 0.5,
            label: 'موضوع',
            fontSize: '14',
            fontWeight: 'bold',
          },
        },
      },
    },
    footers: {
      all: {
        height: 2.5,
        children: {},
      },
      last: {
        height: 3,
        children: {
          signature: {
            top: 1,
            right: 5.25,
            label: 'امضاء',
            fontSize: '14',
            isSignerName: true,
            isSingleLine: false,
            isTitleFullNameFirst: false,
            isFullNameOnly: false,
            fontWeight: 'bold',
            maxSize: '80',
            align: 'center',
          },
        },
      },
    },
    reserved: {
      children: {
        logo: {
          top: 2.5,
          right: -4,
          label: 'لوگو',
          maxSize: '80',
        },
        stamp: {
          top: 2.5,
          right: -4,
          label: 'مهر',
          maxSize: '80',
        },
        letterNumber: {
          top: 4.5,
          right: -4,
          label: 'شماره نامه',
          fontSize: '14',
          fontWeight: 'bold',
        },
        attachment: {
          top: 6.5,
          right: -4,
          label: 'پیوست',
          fontSize: '14',
          fontWeight: 'bold',
        },
        priority: {
          top: 8.5,
          right: -4,
          label: 'اولویت',
          fontSize: '14',
          fontWeight: 'bold',
        },
        confidentiality: {
          top: 10.5,
          right: -4,
          label: 'محرمانگی',
          fontSize: '14',
          fontWeight: 'bold',
        },
        pageNumber: {
          top: 12.5,
          right: -4,
          label: 'شماره صفحه',
          fontSize: '14',
          fontWeight: 'bold',
        },
        cc: {
          top: 14.5,
          right: -4,
          label: 'گیرندگان رونوشت',
          fontSize: '14',
          fontWeight: 'bold',
          isSingleLine: false,
          isFullNameOnly: false,
          isTitleFullNameFirst: false,
        },
      },
    },
  },
};

export { A4, A5 };
