@ECHO OFF
call docker build -t chargoon-develop/com.chargoon.cloud.svc.print:0.0.43 .
call docker tag chargoon-develop/com.chargoon.cloud.svc.print:0.0.43 devbuild-srv.chargoon.net:32001/com.chargoon.cloud.svc.print:0.0.43
call docker push devbuild-srv.chargoon.net:32001/com.chargoon.cloud.svc.print:0.0.43
call type deployment.dev-develop.yaml | ssh -p 22443 -i ../k8s/rsa <EMAIL> kubectl apply -f -
call ssh -p 22443 -i ../k8s/rsa <EMAIL> kubectl rollout restart deployment com-chargoon-cloud-svc-print -n develop
