@ECHO OFF

@REM ECHO INSTALL
@REM call npm install

@REM ECHO APP
@REM call npm run build

ECHO IMG
call docker build -t com.chargoon.cloud.svc.print:0.0.1 .

ECHO TAG
call docker tag com.chargoon.cloud.svc.print:0.0.1 localhost:32000/com.chargoon.cloud.svc.print:0.0.1

ECHO PUSH
call docker push localhost:32000/com.chargoon.cloud.svc.print:0.0.1

if "%~1"=="" goto rollout

ECHO DEPLOY
call kubectl apply -f deployment.local.yaml
goto exit

:rollout
ECHO ROLLOUT
call kubectl rollout restart deployment/com-chargoon-cloud-svc-print

:exit
