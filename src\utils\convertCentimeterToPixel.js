
/**
* Function  convert a Centimeter number to Pixel
* @param    {Number} centimeterValue    number in centimeter
* @return   {Number}        number in pixel
*/
const c2p = (valueCM, dpi = 72) => (valueCM * dpi) / 2.54;

/**
* Function witch iterate over nested object and get all numbers and convert to Pixel
* @param    {Object} obj    nested object with number in centimeter
* @param    {Array} excludes remove special keys from convert
* @return   {Object}        new object and all numbers converted to pixel
*/
const c2pInObject = (obj, excludes) => {
  Object.keys(obj).forEach(function (key) {
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      return c2pInObject(obj[key], excludes);
    }
    if (typeof obj[key] === 'number') {
      if (!excludes || !excludes.includes(key)) {
        obj[key] = +c2p(obj[key]).toFixed(1);
      }
    }
  });
  return obj;
};

module.exports = { c2p, c2pInObject };
