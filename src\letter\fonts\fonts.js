// Get the server port for font URLs
const serverPort = process.env.PORT || 3000;

const fontCss = `
  @font-face {
    font-family: 'sahel';
    src: url('http://localhost:${serverPort}/fonts/sahel.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'vazir';
    src: url('http://localhost:${serverPort}/fonts/vazir.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'b-titr';
    src: url('http://localhost:${serverPort}/fonts/b-titr.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'b-traffic';
    src: url('http://localhost:${serverPort}/fonts/b-traffic.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'b-zar';
    src: url('http://localhost:${serverPort}/fonts/b-zar.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'lotus';
    src: url('http://localhost:${serverPort}/fonts/lotus.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'mitra';
    src: url('http://localhost:${serverPort}/fonts/mitra.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'myriad-pro';
    src: url('http://localhost:${serverPort}/fonts/myriad-pro.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'nazanin';
    src: url('http://localhost:${serverPort}/fonts/nazanin.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'yaghot';
    src: url('http://localhost:${serverPort}/fonts/yaghot.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'yaghot-bold';
    src: url('http://localhost:${serverPort}/fonts/yaghotBold.otf') format('opentype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'yekan';
    src: url('http://localhost:${serverPort}/fonts/yekan.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }
`;

module.exports = { fontCss };
