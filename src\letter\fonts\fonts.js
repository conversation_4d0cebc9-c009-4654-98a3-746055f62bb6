const path = require('path');

// Get absolute paths to font files
const fontDir = path.resolve(process.cwd(), 'src/letter/fonts').replace(/\\/g, '/');

const fontCss = `
  @font-face {
    font-family: 'sahel';
    src: url('file://${fontDir}/sahel.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'vazir';
    src: url('file://${fontDir}/vazir.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'b-titr';
    src: url('file://${fontDir}/b-titr.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'b-traffic';
    src: url('file://${fontDir}/b-traffic.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'b-zar';
    src: url('file://${fontDir}/b-zar.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'lotus';
    src: url('file://${fontDir}/lotus.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'mitra';
    src: url('file://${fontDir}/mitra.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'myriad-pro';
    src: url('file://${fontDir}/myriad-pro.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'nazanin';
    src: url('file://${fontDir}/nazanin.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'yaghot';
    src: url('file://${fontDir}/yaghot.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'yaghot-bold';
    src: url('file://${fontDir}/yaghotBold.otf') format('opentype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'yekan';
    src: url('file://${fontDir}/yekan.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }
`;

module.exports = { fontCss };
