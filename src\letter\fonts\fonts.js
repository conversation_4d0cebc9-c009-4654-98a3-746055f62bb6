// Get absolute file path
// const path = require('node:path');

import fonts from "./fonts2";

// const fontFilePath = path.join(__dirname, 'sahel.otf');

// console.log(fontFilePath);
// console.log(fontFilePath.replace(/\\/g, '/'));

export const fontCss = `
  @font-face {
    font-family: 'sahel';
    src: url(data:font/otf;base64,${fonts.sahel}) format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'vazir';
    src: url(data:font/otf;base64,${fonts.vazir}) format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }
`;
